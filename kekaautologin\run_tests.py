#!/usr/bin/env python3
"""
Test runner for Keka Auto Login application.
Runs all unit tests and generates a comprehensive test report.
"""

import unittest
import sys
import os
from pathlib import Path
import time
from io import StringIO

# Add src to path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))

# Import test modules
from tests.test_credential_manager import TestCredentialManager
from tests.test_scheduler import TestAttendanceScheduler
from tests.test_error_handler import TestErrorHandler


class TestResult:
    """Custom test result class to capture detailed information."""
    
    def __init__(self):
        self.tests_run = 0
        self.failures = []
        self.errors = []
        self.skipped = []
        self.success_count = 0
        self.start_time = None
        self.end_time = None
    
    def start_test(self, test):
        """Called when a test starts."""
        if self.start_time is None:
            self.start_time = time.time()
    
    def add_success(self, test):
        """Called when a test passes."""
        self.success_count += 1
    
    def add_failure(self, test, err):
        """Called when a test fails."""
        self.failures.append((test, err))
    
    def add_error(self, test, err):
        """Called when a test has an error."""
        self.errors.append((test, err))
    
    def add_skip(self, test, reason):
        """Called when a test is skipped."""
        self.skipped.append((test, reason))
    
    def stop_test(self, test):
        """Called when a test ends."""
        self.tests_run += 1
        self.end_time = time.time()


def run_test_suite():
    """Run all tests and return results."""
    print("=" * 70)
    print("KEKA AUTO LOGIN - TEST SUITE")
    print("=" * 70)
    print()
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestCredentialManager,
        TestAttendanceScheduler,
        TestErrorHandler
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests with custom result handler
    stream = StringIO()
    runner = unittest.TextTestRunner(
        stream=stream,
        verbosity=2,
        buffer=True
    )
    
    print("Running tests...")
    print("-" * 50)
    
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    # Print results
    print(f"\nTest execution completed in {end_time - start_time:.2f} seconds")
    print("=" * 70)
    
    # Summary
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped)
    passed = total_tests - failures - errors - skipped
    
    print(f"SUMMARY:")
    print(f"  Total Tests: {total_tests}")
    print(f"  Passed: {passed}")
    print(f"  Failed: {failures}")
    print(f"  Errors: {errors}")
    print(f"  Skipped: {skipped}")
    print()
    
    # Success rate
    if total_tests > 0:
        success_rate = (passed / total_tests) * 100
        print(f"Success Rate: {success_rate:.1f}%")
    
    # Detailed failure/error reporting
    if result.failures:
        print("\n" + "=" * 70)
        print("FAILURES:")
        print("=" * 70)
        for test, traceback in result.failures:
            print(f"\nFAILED: {test}")
            print("-" * 50)
            print(traceback)
    
    if result.errors:
        print("\n" + "=" * 70)
        print("ERRORS:")
        print("=" * 70)
        for test, traceback in result.errors:
            print(f"\nERROR: {test}")
            print("-" * 50)
            print(traceback)
    
    # Test coverage by module
    print("\n" + "=" * 70)
    print("TEST COVERAGE BY MODULE:")
    print("=" * 70)
    
    module_results = {}
    for test_class in test_classes:
        module_name = test_class.__module__.split('.')[-1]
        class_tests = [t for t in result.testsRun if test_class.__name__ in str(t)]
        module_results[module_name] = {
            'total': len([t for t in suite if test_class.__name__ in str(t)]),
            'passed': len([t for t in class_tests if t not in [f[0] for f in result.failures + result.errors]])
        }
    
    for module, stats in module_results.items():
        if stats['total'] > 0:
            coverage = (stats['passed'] / stats['total']) * 100
            print(f"  {module}: {stats['passed']}/{stats['total']} ({coverage:.1f}%)")
    
    print("\n" + "=" * 70)
    
    # Return overall success
    return len(result.failures) == 0 and len(result.errors) == 0


def check_dependencies():
    """Check if all required dependencies are available."""
    print("Checking dependencies...")
    
    required_modules = [
        'cryptography',
        'selenium',
        'schedule',
        'requests',
        'toga'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✓ {module}")
        except ImportError:
            print(f"  ✗ {module} (missing)")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\nWarning: Missing modules: {', '.join(missing_modules)}")
        print("Some tests may fail due to missing dependencies.")
        return False
    
    print("All dependencies available.")
    return True


def main():
    """Main test runner function."""
    print("Keka Auto Login - Test Suite Runner")
    print("=" * 70)
    
    # Check dependencies
    deps_ok = check_dependencies()
    print()
    
    if not deps_ok:
        response = input("Continue with tests despite missing dependencies? (y/N): ")
        if response.lower() != 'y':
            print("Exiting...")
            return False
    
    # Run tests
    success = run_test_suite()
    
    if success:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed. Please review the output above.")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
