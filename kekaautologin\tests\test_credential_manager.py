"""
Unit tests for CredentialManager module.
Tests encryption, decryption, and credential storage functionality.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from kekaautologin.credential_manager import CredentialManager


class TestCredentialManager(unittest.TestCase):
    """Test cases for CredentialManager."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test credentials
        self.test_dir = tempfile.mkdtemp()
        self.credential_manager = CredentialManager()
        
        # Override config directory for testing
        self.credential_manager.config_dir = Path(self.test_dir)
        self.credential_manager.credentials_file = self.credential_manager.config_dir / "credentials.enc"
    
    def tearDown(self):
        """Clean up test environment."""
        # Remove temporary directory
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_save_and_load_credentials(self):
        """Test saving and loading credentials."""
        username = "test_user"
        password = "test_password"
        master_password = "master123"
        
        # Save credentials
        result = self.credential_manager.save_credentials(username, password, master_password)
        self.assertTrue(result, "Failed to save credentials")
        
        # Check if credentials file exists
        self.assertTrue(self.credential_manager.credentials_file.exists(), "Credentials file not created")
        
        # Load credentials
        loaded_credentials = self.credential_manager.load_credentials(master_password)
        self.assertIsNotNone(loaded_credentials, "Failed to load credentials")
        self.assertEqual(loaded_credentials['username'], username, "Username mismatch")
        self.assertEqual(loaded_credentials['password'], password, "Password mismatch")
    
    def test_wrong_master_password(self):
        """Test loading credentials with wrong master password."""
        username = "test_user"
        password = "test_password"
        master_password = "master123"
        wrong_password = "wrong123"
        
        # Save credentials
        self.credential_manager.save_credentials(username, password, master_password)
        
        # Try to load with wrong password
        loaded_credentials = self.credential_manager.load_credentials(wrong_password)
        self.assertIsNone(loaded_credentials, "Should fail with wrong master password")
    
    def test_verify_master_password(self):
        """Test master password verification."""
        username = "test_user"
        password = "test_password"
        master_password = "master123"
        
        # Save credentials
        self.credential_manager.save_credentials(username, password, master_password)
        
        # Verify correct password
        self.assertTrue(
            self.credential_manager.verify_master_password(master_password),
            "Should verify correct master password"
        )
        
        # Verify wrong password
        self.assertFalse(
            self.credential_manager.verify_master_password("wrong123"),
            "Should reject wrong master password"
        )
    
    def test_credentials_exist(self):
        """Test checking if credentials exist."""
        # Initially no credentials
        self.assertFalse(
            self.credential_manager.credentials_exist(),
            "Should return False when no credentials exist"
        )
        
        # Save credentials
        self.credential_manager.save_credentials("user", "pass", "master")
        
        # Now credentials should exist
        self.assertTrue(
            self.credential_manager.credentials_exist(),
            "Should return True when credentials exist"
        )
    
    def test_delete_credentials(self):
        """Test deleting credentials."""
        # Save credentials
        self.credential_manager.save_credentials("user", "pass", "master")
        self.assertTrue(self.credential_manager.credentials_exist())
        
        # Delete credentials
        result = self.credential_manager.delete_credentials()
        self.assertTrue(result, "Failed to delete credentials")
        self.assertFalse(self.credential_manager.credentials_exist(), "Credentials still exist after deletion")
    
    def test_empty_credentials(self):
        """Test handling of empty credentials."""
        # Test empty username
        result = self.credential_manager.save_credentials("", "password", "master")
        self.assertFalse(result, "Should reject empty username")
        
        # Test empty password
        result = self.credential_manager.save_credentials("username", "", "master")
        self.assertFalse(result, "Should reject empty password")
        
        # Test empty master password
        result = self.credential_manager.save_credentials("username", "password", "")
        self.assertFalse(result, "Should reject empty master password")
    
    def test_encryption_consistency(self):
        """Test that encryption produces consistent results."""
        username = "test_user"
        password = "test_password"
        master_password = "master123"
        
        # Save and load multiple times
        for i in range(3):
            self.credential_manager.save_credentials(username, password, master_password)
            loaded = self.credential_manager.load_credentials(master_password)
            
            self.assertIsNotNone(loaded, f"Failed to load credentials on iteration {i}")
            self.assertEqual(loaded['username'], username, f"Username mismatch on iteration {i}")
            self.assertEqual(loaded['password'], password, f"Password mismatch on iteration {i}")
    
    def test_unicode_credentials(self):
        """Test handling of unicode characters in credentials."""
        username = "用户名"  # Chinese characters
        password = "пароль"  # Cyrillic characters
        master_password = "мастер123"
        
        # Save credentials with unicode
        result = self.credential_manager.save_credentials(username, password, master_password)
        self.assertTrue(result, "Failed to save unicode credentials")
        
        # Load and verify
        loaded = self.credential_manager.load_credentials(master_password)
        self.assertIsNotNone(loaded, "Failed to load unicode credentials")
        self.assertEqual(loaded['username'], username, "Unicode username mismatch")
        self.assertEqual(loaded['password'], password, "Unicode password mismatch")


if __name__ == '__main__':
    unittest.main()
