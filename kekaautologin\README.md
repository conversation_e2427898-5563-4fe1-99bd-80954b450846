# Keka Auto Login - Android App

An automated attendance system for Keka (https://dhinwa.keka.com) built with Python using BeeWare/Briefcase for cross-platform deployment.

## Features

- **Secure Credential Storage**: AES-256 encrypted credential storage with PBKDF2 key derivation
- **Automated Scheduling**: Background service for automatic clock-in (10:00 AM) and clock-out (8:00 PM)
- **Manual Controls**: Manual trigger buttons for immediate clock-in/out operations
- **Web Automation**: Selenium-based headless browser automation for Keka website interaction
- **Error Handling**: Comprehensive logging, retry mechanisms, and network connectivity checks
- **Cross-Platform UI**: Native mobile interface using Toga (BeeWare's UI toolkit)
- **Android Integration**: Foreground service, AlarmManager, and proper permissions for Android 10+

## Project Structure

```
kekaautologin/
├── src/kekaautologin/
│   ├── app.py                 # Main Toga application with UI
│   ├── credential_manager.py  # Secure credential storage
│   ├── keka_automation.py     # Selenium web automation
│   ├── scheduler.py           # Background scheduling
│   ├── android_service.py     # Android-specific services
│   └── error_handler.py       # Error handling and logging
├── tests/                     # Unit tests
├── demo.py                    # Demo script
├── run_tests.py              # Test runner
└── pyproject.toml            # Project configuration
```

## Installation & Setup

### Prerequisites
- Python 3.8+
- Chrome browser (for web automation)
- Android SDK (for Android deployment)

### Install Dependencies
```bash
cd kekaautologin
pip install -e .
```

### Run Demo
```bash
python demo.py
```

### Run Tests
```bash
python -m unittest tests.test_credential_manager -v
python -m unittest tests.test_scheduler -v
python -m unittest tests.test_error_handler -v
```

## Usage

### Desktop Development
```bash
briefcase dev
```

### Android Deployment
```bash
# Build for Android
briefcase android

# Create APK
briefcase android package

# Install on device
briefcase android run
```

## Core Components

### 1. Credential Manager (`credential_manager.py`)
- **Encryption**: AES-256 with Fernet (symmetric encryption)
- **Key Derivation**: PBKDF2 with SHA-256, 100,000 iterations
- **Storage**: Cross-platform configuration directory
- **Security**: Master password required for all operations

**Key Methods:**
- `save_credentials(username, password, master_password)` - Encrypt and save credentials
- `load_credentials(master_password)` - Decrypt and load credentials
- `verify_master_password(master_password)` - Verify master password
- `delete_credentials()` - Securely delete stored credentials

### 2. Web Automation (`keka_automation.py`)
- **Browser**: Chrome headless with Android-compatible options
- **Automation**: Selenium WebDriver with robust element selection
- **Error Handling**: Multiple selector strategies and retry mechanisms
- **Mobile Support**: Configured for mobile viewport and touch interactions

**Key Methods:**
- `login(username, password)` - Authenticate with Keka
- `clock_in()` - Perform clock-in operation
- `clock_out()` - Perform clock-out operation
- `get_attendance_status()` - Check current attendance status

### 3. Scheduler (`scheduler.py`)
- **Scheduling**: Python `schedule` library with threading
- **Default Times**: 10:00 AM clock-in, 8:00 PM clock-out
- **Background**: Non-blocking scheduler thread
- **Callbacks**: UI status updates and error notifications

**Key Methods:**
- `start_scheduler(master_password)` - Start background scheduling
- `stop_scheduler()` - Stop background scheduling
- `manual_clock_in(master_password)` - Manual clock-in trigger
- `manual_clock_out(master_password)` - Manual clock-out trigger
- `set_schedule_times(clock_in_time, clock_out_time)` - Update schedule

### 4. Android Service (`android_service.py`)
- **Foreground Service**: Android 10+ compatible background execution
- **AlarmManager**: System-level alarm scheduling
- **Permissions**: Automatic permission requests
- **Notifications**: Persistent notification for foreground service

**Key Methods:**
- `request_permissions()` - Request Android permissions
- `start_foreground_service()` - Start foreground service
- `schedule_alarms(clock_in_time, clock_out_time)` - Schedule system alarms
- `cancel_alarms()` - Cancel scheduled alarms

### 5. Error Handler (`error_handler.py`)
- **Logging**: Separate main and error log files
- **Retry Logic**: Exponential backoff retry mechanism
- **Network Checks**: Connectivity validation before operations
- **Safe Execution**: Exception-safe function execution

**Key Methods:**
- `log_info(message)` - Log informational messages
- `log_error(message, exception)` - Log errors with stack traces
- `retry_with_backoff(func, *args, **kwargs)` - Retry with exponential backoff
- `check_network_connectivity()` - Validate internet connection
- `safe_execute(func, *args, **kwargs)` - Safe function execution

### 6. UI Application (`app.py`)
- **Framework**: Toga (BeeWare's native UI toolkit)
- **Screens**: Setup, login, and dashboard screens
- **Controls**: Manual trigger buttons, schedule configuration
- **Integration**: All backend components integrated

**Key Features:**
- Initial credential setup screen
- Master password login screen
- Dashboard with status display and manual controls
- Schedule time configuration
- Real-time status updates

## Security Features

1. **Encryption**: AES-256 encryption for all stored credentials
2. **Key Derivation**: PBKDF2 with 100,000 iterations prevents brute force
3. **Master Password**: Required for all credential operations
4. **No Plain Text**: Credentials never stored in plain text
5. **Secure Deletion**: Proper cleanup of sensitive data

## Android Permissions

The app requests the following permissions:
- `WAKE_LOCK` - Keep device awake during operations
- `RECEIVE_BOOT_COMPLETED` - Start service on device boot
- `SCHEDULE_EXACT_ALARM` - Schedule precise alarms
- `FOREGROUND_SERVICE` - Run background service
- `POST_NOTIFICATIONS` - Show service notifications
- `INTERNET` - Network access for Keka website
- `ACCESS_NETWORK_STATE` - Check network connectivity

## Testing

The project includes comprehensive unit tests:

- **Credential Manager Tests**: Encryption, decryption, validation
- **Scheduler Tests**: Scheduling, manual operations, callbacks
- **Error Handler Tests**: Logging, retry mechanisms, network checks

Run all tests:
```bash
python run_tests.py
```

## Deployment

### Android APK
1. Configure Android SDK path in Briefcase
2. Build: `briefcase android`
3. Package: `briefcase android package`
4. Install: `briefcase android run`

### Development Testing
1. Run demo: `python demo.py`
2. Run desktop app: `briefcase dev`
3. Run individual tests: `python -m unittest tests.test_* -v`

## Configuration

### Schedule Times
Default schedule can be modified in the UI or programmatically:
- Clock In: 10:00 AM
- Clock Out: 8:00 PM

### Logging
Logs are stored in platform-specific directories:
- Windows: `%APPDATA%/KekaAutoLogin/logs/`
- Linux/macOS: `~/.local/share/KekaAutoLogin/logs/`

### Credentials
Encrypted credentials are stored in:
- Windows: `%APPDATA%/KekaAutoLogin/credentials.enc`
- Linux/macOS: `~/.local/share/KekaAutoLogin/credentials.enc`

## Troubleshooting

### Common Issues
1. **Chrome not found**: Install Chrome browser
2. **Network errors**: Check internet connectivity
3. **Login failures**: Verify Keka credentials
4. **Android permissions**: Grant all requested permissions

### Debug Mode
Enable debug logging by setting environment variable:
```bash
export KEKA_DEBUG=1
```

## License

This project is for educational and personal use only. Please ensure compliance with your organization's policies regarding automated attendance systems.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Run the demo script to verify functionality
4. Check unit tests for component validation
