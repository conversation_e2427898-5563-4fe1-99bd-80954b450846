"""
Background scheduler for automated Keka attendance operations.
Handles scheduling of clock-in and clock-out operations.
"""

import threading
import time
import logging
from datetime import datetime, time as dt_time
from typing import Callable, Optional
import schedule

from .keka_automation import KekaAutomation
from .credential_manager import CredentialManager
from .error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, with_error_handling, with_retry


class AttendanceScheduler:
    """Manages scheduled attendance operations."""
    
    def __init__(self, credential_manager: CredentialManager):
        self.credential_manager = credential_manager
        self.automation = None
        self.scheduler_thread = None
        self.running = False
        self.error_handler = ErrorHandler()
        self.logger = self.error_handler.logger
        
        # Default schedule times
        self.clock_in_time = dt_time(10, 0)  # 10:00 AM
        self.clock_out_time = dt_time(20, 0)  # 8:00 PM
        
        # Callbacks for UI updates
        self.status_callback: Optional[Callable] = None
        self.error_callback: Optional[Callable] = None
    

    
    def set_schedule_times(self, clock_in_time: dt_time, clock_out_time: dt_time):
        """Set custom schedule times."""
        self.clock_in_time = clock_in_time
        self.clock_out_time = clock_out_time
        self.logger.info(f"Schedule updated: Clock In at {clock_in_time}, Clock Out at {clock_out_time}")
    
    def set_callbacks(self, status_callback: Callable = None, error_callback: Callable = None):
        """Set callback functions for UI updates."""
        self.status_callback = status_callback
        self.error_callback = error_callback
    
    def _notify_status(self, message: str):
        """Notify status update."""
        self.logger.info(message)
        if self.status_callback:
            try:
                self.status_callback(message)
            except Exception as e:
                self.logger.error(f"Error in status callback: {e}")
    
    def _notify_error(self, error: str):
        """Notify error."""
        self.logger.error(error)
        if self.error_callback:
            try:
                self.error_callback(error)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
    
    @with_retry(ErrorHandler(), max_retries=3)
    def _perform_clock_in(self, master_password: str):
        """Perform scheduled clock-in operation with retry mechanism."""
        try:
            self._notify_status("Starting scheduled clock-in...")

            # Check network connectivity first
            if not self.error_handler.check_network_connectivity():
                if not self.error_handler.wait_for_network(max_wait_time=300):
                    self._notify_error("Clock-in failed: No network connectivity")
                    return False

            # Load credentials
            credentials = self.credential_manager.load_credentials(master_password)
            if not credentials:
                self._notify_error("Failed to load credentials for clock-in")
                return False

            # Initialize automation
            if not self.automation:
                self.automation = KekaAutomation(headless=True)

            # Perform login and clock-in with error handling
            success, result = self.error_handler.safe_execute(
                self.automation.login, credentials['username'], credentials['password']
            )

            if success and result:
                success, result = self.error_handler.safe_execute(self.automation.clock_in)
                if success and result:
                    self._notify_status(f"Clock-in successful at {datetime.now().strftime('%H:%M:%S')}")
                    self.error_handler.log_info("Scheduled clock-in completed successfully")
                    return True
                else:
                    self._notify_error("Clock-in operation failed")
                    self.error_handler.log_error("Clock-in operation failed", result if isinstance(result, Exception) else None)
            else:
                self._notify_error("Login failed for clock-in")
                self.error_handler.log_error("Login failed for clock-in", result if isinstance(result, Exception) else None)

            return False

        except Exception as e:
            self._notify_error(f"Clock-in error: {e}")
            self.error_handler.log_error("Clock-in operation error", e)
            return False
    
    @with_retry(ErrorHandler(), max_retries=3)
    def _perform_clock_out(self, master_password: str):
        """Perform scheduled clock-out operation with retry mechanism."""
        try:
            self._notify_status("Starting scheduled clock-out...")

            # Check network connectivity first
            if not self.error_handler.check_network_connectivity():
                if not self.error_handler.wait_for_network(max_wait_time=300):
                    self._notify_error("Clock-out failed: No network connectivity")
                    return False

            # Load credentials
            credentials = self.credential_manager.load_credentials(master_password)
            if not credentials:
                self._notify_error("Failed to load credentials for clock-out")
                return False

            # Initialize automation
            if not self.automation:
                self.automation = KekaAutomation(headless=True)

            # Perform login and clock-out with error handling
            success, result = self.error_handler.safe_execute(
                self.automation.login, credentials['username'], credentials['password']
            )

            if success and result:
                success, result = self.error_handler.safe_execute(self.automation.clock_out)
                if success and result:
                    self._notify_status(f"Clock-out successful at {datetime.now().strftime('%H:%M:%S')}")
                    self.error_handler.log_info("Scheduled clock-out completed successfully")
                    return True
                else:
                    self._notify_error("Clock-out operation failed")
                    self.error_handler.log_error("Clock-out operation failed", result if isinstance(result, Exception) else None)
            else:
                self._notify_error("Login failed for clock-out")
                self.error_handler.log_error("Login failed for clock-out", result if isinstance(result, Exception) else None)

            return False

        except Exception as e:
            self._notify_error(f"Clock-out error: {e}")
            self.error_handler.log_error("Clock-out operation error", e)
            return False
    
    def manual_clock_in(self, master_password: str):
        """Manually trigger clock-in operation."""
        self.error_handler.log_info("Manual clock-in triggered")
        return self._perform_clock_in(master_password)

    def manual_clock_out(self, master_password: str):
        """Manually trigger clock-out operation."""
        self.error_handler.log_info("Manual clock-out triggered")
        return self._perform_clock_out(master_password)
    
    def start_scheduler(self, master_password: str):
        """Start the background scheduler."""
        if self.running:
            self.logger.warning("Scheduler is already running")
            return
        
        # Clear any existing schedules
        schedule.clear()
        
        # Schedule clock-in and clock-out
        schedule.every().day.at(self.clock_in_time.strftime('%H:%M')).do(
            self._perform_clock_in, master_password
        )
        schedule.every().day.at(self.clock_out_time.strftime('%H:%M')).do(
            self._perform_clock_out, master_password
        )
        
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        self._notify_status(f"Scheduler started - Clock In: {self.clock_in_time}, Clock Out: {self.clock_out_time}")
    
    def stop_scheduler(self):
        """Stop the background scheduler."""
        self.running = False
        schedule.clear()
        
        if self.automation:
            self.automation.close()
            self.automation = None
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        self._notify_status("Scheduler stopped")
    
    def _run_scheduler(self):
        """Run the scheduler in background thread."""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                self.logger.error(f"Scheduler error: {e}")
                time.sleep(60)
    
    def get_next_scheduled_times(self):
        """Get next scheduled clock-in and clock-out times."""
        now = datetime.now()
        today = now.date()
        
        # Calculate next clock-in time
        next_clock_in = datetime.combine(today, self.clock_in_time)
        if next_clock_in <= now:
            next_clock_in = datetime.combine(today.replace(day=today.day + 1), self.clock_in_time)
        
        # Calculate next clock-out time
        next_clock_out = datetime.combine(today, self.clock_out_time)
        if next_clock_out <= now:
            next_clock_out = datetime.combine(today.replace(day=today.day + 1), self.clock_out_time)
        
        return {
            'next_clock_in': next_clock_in,
            'next_clock_out': next_clock_out
        }
    
    def is_running(self):
        """Check if scheduler is running."""
        return self.running
    
    def get_status(self):
        """Get current scheduler status."""
        if not self.running:
            return "Stopped"
        
        next_times = self.get_next_scheduled_times()
        return f"Running - Next: Clock In at {next_times['next_clock_in'].strftime('%H:%M')}, Clock Out at {next_times['next_clock_out'].strftime('%H:%M')}"
