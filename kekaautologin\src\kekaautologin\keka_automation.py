"""
Keka web automation module for automated login and attendance management.
Handles browser automation using Selenium WebDriver.
"""

import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class KekaAutomation:
    """Handles automated Keka login and attendance operations."""
    
    def __init__(self, headless=True, timeout=30):
        self.headless = headless
        self.timeout = timeout
        self.driver = None
        self.logger = self._setup_logger()
        
        # Keka URLs
        self.base_url = "https://dhinwa.keka.com"
        self.login_url = f"{self.base_url}/#/login"
        self.attendance_url = f"{self.base_url}/#/me/attendance/logs"
    
    def _setup_logger(self):
        """Setup logging for automation activities."""
        logger = logging.getLogger('KekaAutomation')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _setup_driver(self):
        """Setup Chrome WebDriver with appropriate options."""
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument('--headless')
            
            # Additional options for better compatibility
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            
            # For Android compatibility
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            
            self.logger.info("WebDriver setup successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup WebDriver: {e}")
            return False
    
    def login(self, username, password):
        """
        Perform login to Keka.
        
        Args:
            username: Keka username
            password: Keka password
            
        Returns:
            bool: True if login successful, False otherwise
        """
        try:
            if not self.driver:
                if not self._setup_driver():
                    return False
            
            self.logger.info("Navigating to Keka login page")
            self.driver.get(self.login_url)
            
            # Wait for login form to load
            wait = WebDriverWait(self.driver, self.timeout)
            
            # Find and fill username field
            username_field = wait.until(
                EC.presence_of_element_located((By.ID, "email"))
            )
            username_field.clear()
            username_field.send_keys(username)
            self.logger.info("Username entered")
            
            # Find and fill password field
            password_field = self.driver.find_element(By.ID, "password")
            password_field.clear()
            password_field.send_keys(password)
            self.logger.info("Password entered")
            
            # Click login button
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            self.logger.info("Login button clicked")
            
            # Wait for login to complete (check for dashboard or attendance page)
            try:
                wait.until(
                    EC.any_of(
                        EC.url_contains("dashboard"),
                        EC.url_contains("attendance"),
                        EC.url_contains("me")
                    )
                )
                self.logger.info("Login successful")
                return True
                
            except TimeoutException:
                # Check for error messages
                try:
                    error_element = self.driver.find_element(
                        By.XPATH, "//div[contains(@class, 'error') or contains(@class, 'alert')]"
                    )
                    error_text = error_element.text
                    self.logger.error(f"Login failed: {error_text}")
                except NoSuchElementException:
                    self.logger.error("Login failed: Unknown error")
                return False
                
        except Exception as e:
            self.logger.error(f"Login error: {e}")
            return False
    
    def navigate_to_attendance(self):
        """Navigate to attendance page."""
        try:
            self.logger.info("Navigating to attendance page")
            self.driver.get(self.attendance_url)
            
            # Wait for attendance page to load
            wait = WebDriverWait(self.driver, self.timeout)
            wait.until(
                EC.any_of(
                    EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Clock In')]")),
                    EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Clock Out')]")),
                    EC.presence_of_element_located((By.CLASS_NAME, "attendance"))
                )
            )
            self.logger.info("Attendance page loaded")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to navigate to attendance: {e}")
            return False
    
    def clock_in(self):
        """Perform clock-in operation."""
        try:
            if not self.navigate_to_attendance():
                return False
            
            # Look for Clock In button
            wait = WebDriverWait(self.driver, self.timeout)
            
            # Try different possible selectors for clock in button
            clock_in_selectors = [
                "//button[contains(text(), 'Clock In')]",
                "//button[contains(text(), 'CLOCK IN')]",
                "//button[contains(@class, 'clock-in')]",
                "//div[contains(@class, 'clock-in')]//button",
                "//button[contains(text(), 'Punch In')]"
            ]
            
            for selector in clock_in_selectors:
                try:
                    clock_in_button = wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    clock_in_button.click()
                    self.logger.info("Clock In button clicked")
                    
                    # Wait a moment for the action to complete
                    time.sleep(3)
                    return True
                    
                except TimeoutException:
                    continue
            
            self.logger.error("Clock In button not found")
            return False
            
        except Exception as e:
            self.logger.error(f"Clock in error: {e}")
            return False
    
    def clock_out(self):
        """Perform clock-out operation."""
        try:
            if not self.navigate_to_attendance():
                return False
            
            # Look for Clock Out button
            wait = WebDriverWait(self.driver, self.timeout)
            
            # Try different possible selectors for clock out button
            clock_out_selectors = [
                "//button[contains(text(), 'Clock Out')]",
                "//button[contains(text(), 'CLOCK OUT')]",
                "//button[contains(@class, 'clock-out')]",
                "//div[contains(@class, 'clock-out')]//button",
                "//button[contains(text(), 'Punch Out')]"
            ]
            
            for selector in clock_out_selectors:
                try:
                    clock_out_button = wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    clock_out_button.click()
                    self.logger.info("Clock Out button clicked")
                    
                    # Wait a moment for the action to complete
                    time.sleep(3)
                    return True
                    
                except TimeoutException:
                    continue
            
            self.logger.error("Clock Out button not found")
            return False
            
        except Exception as e:
            self.logger.error(f"Clock out error: {e}")
            return False
    
    def get_attendance_status(self):
        """Get current attendance status."""
        try:
            if not self.navigate_to_attendance():
                return None
            
            # Try to determine current status based on available buttons
            try:
                self.driver.find_element(By.XPATH, "//button[contains(text(), 'Clock In')]")
                return "clocked_out"
            except NoSuchElementException:
                pass
            
            try:
                self.driver.find_element(By.XPATH, "//button[contains(text(), 'Clock Out')]")
                return "clocked_in"
            except NoSuchElementException:
                pass
            
            return "unknown"
            
        except Exception as e:
            self.logger.error(f"Error getting attendance status: {e}")
            return None
    
    def close(self):
        """Close the browser driver."""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("WebDriver closed")
            except Exception as e:
                self.logger.error(f"Error closing WebDriver: {e}")
            finally:
                self.driver = None
