"""
Comprehensive error handling, logging, and retry mechanisms for Keka Auto Login.
Includes network connectivity checks and robust error recovery.
"""

import os
import time
import logging
import requests
from pathlib import Path
from datetime import datetime, timedelta
from typing import Callable, Optional, Any
from functools import wraps


class ErrorHandler:
    """Centralized error handling and logging system."""
    
    def __init__(self, app_name="KekaAutoLogin"):
        self.app_name = app_name
        self.log_dir = self._get_log_directory()
        self.log_file = self.log_dir / "keka_auto_login.log"
        self.error_log_file = self.log_dir / "errors.log"
        
        # Ensure log directory exists
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup loggers
        self.logger = self._setup_main_logger()
        self.error_logger = self._setup_error_logger()
        
        # Retry configuration
        self.max_retries = 3
        self.retry_delay = 5  # seconds
        self.backoff_multiplier = 2
        
        # Network check configuration
        self.network_check_urls = [
            "https://www.google.com",
            "https://www.cloudflare.com",
            "https://dhinwa.keka.com"
        ]
        self.network_timeout = 10
    
    def _get_log_directory(self):
        """Get platform-specific log directory."""
        if os.name == 'nt':  # Windows
            log_dir = Path(os.environ.get('APPDATA', '')) / self.app_name / 'logs'
        elif os.name == 'posix':  # Unix/Linux/macOS
            log_dir = Path.home() / '.local' / 'share' / self.app_name / 'logs'
        else:
            log_dir = Path.home() / f'.{self.app_name}' / 'logs'
        
        return log_dir
    
    def _setup_main_logger(self):
        """Setup main application logger."""
        logger = logging.getLogger('KekaAutoLogin')
        logger.setLevel(logging.INFO)
        
        # Remove existing handlers to avoid duplicates
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def _setup_error_logger(self):
        """Setup dedicated error logger."""
        error_logger = logging.getLogger('KekaAutoLogin.Errors')
        error_logger.setLevel(logging.ERROR)
        
        # Remove existing handlers
        for handler in error_logger.handlers[:]:
            error_logger.removeHandler(handler)
        
        # Error file handler
        error_handler = logging.FileHandler(self.error_log_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        
        # Detailed formatter for errors
        error_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        error_handler.setFormatter(error_formatter)
        
        error_logger.addHandler(error_handler)
        
        return error_logger
    
    def log_info(self, message: str):
        """Log info message."""
        self.logger.info(message)
    
    def log_warning(self, message: str):
        """Log warning message."""
        self.logger.warning(message)
    
    def log_error(self, message: str, exception: Exception = None):
        """Log error message with optional exception details."""
        if exception:
            self.error_logger.error(f"{message}: {str(exception)}", exc_info=True)
        else:
            self.error_logger.error(message)
        
        # Also log to main logger
        self.logger.error(message)
    
    def check_network_connectivity(self) -> bool:
        """Check if network connectivity is available."""
        for url in self.network_check_urls:
            try:
                response = requests.get(
                    url,
                    timeout=self.network_timeout,
                    headers={'User-Agent': 'KekaAutoLogin/1.0'}
                )
                if response.status_code == 200:
                    self.log_info(f"Network connectivity confirmed via {url}")
                    return True
            except requests.RequestException:
                continue
        
        self.log_error("No network connectivity detected")
        return False
    
    def wait_for_network(self, max_wait_time: int = 300) -> bool:
        """Wait for network connectivity with timeout."""
        start_time = time.time()
        check_interval = 30  # Check every 30 seconds
        
        self.log_info("Waiting for network connectivity...")
        
        while time.time() - start_time < max_wait_time:
            if self.check_network_connectivity():
                return True
            
            self.log_info(f"Network not available, retrying in {check_interval} seconds...")
            time.sleep(check_interval)
        
        self.log_error(f"Network connectivity timeout after {max_wait_time} seconds")
        return False
    
    def retry_with_backoff(self, 
                          func: Callable, 
                          *args, 
                          max_retries: Optional[int] = None,
                          retry_delay: Optional[int] = None,
                          **kwargs) -> Any:
        """
        Execute function with exponential backoff retry mechanism.
        
        Args:
            func: Function to execute
            *args: Function arguments
            max_retries: Maximum number of retries (uses default if None)
            retry_delay: Initial retry delay (uses default if None)
            **kwargs: Function keyword arguments
            
        Returns:
            Function result or raises last exception
        """
        max_retries = max_retries or self.max_retries
        retry_delay = retry_delay or self.retry_delay
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    self.log_info(f"Retry attempt {attempt}/{max_retries} for {func.__name__}")
                
                result = func(*args, **kwargs)
                
                if attempt > 0:
                    self.log_info(f"Function {func.__name__} succeeded on attempt {attempt + 1}")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                if attempt < max_retries:
                    delay = retry_delay * (self.backoff_multiplier ** attempt)
                    self.log_warning(
                        f"Function {func.__name__} failed on attempt {attempt + 1}: {str(e)}. "
                        f"Retrying in {delay} seconds..."
                    )
                    time.sleep(delay)
                else:
                    self.log_error(
                        f"Function {func.__name__} failed after {max_retries + 1} attempts",
                        e
                    )
        
        # If we get here, all retries failed
        raise last_exception
    
    def safe_execute(self, func: Callable, *args, **kwargs) -> tuple[bool, Any]:
        """
        Safely execute a function and return success status and result.
        
        Returns:
            tuple: (success: bool, result: Any or exception)
        """
        try:
            result = func(*args, **kwargs)
            return True, result
        except Exception as e:
            self.log_error(f"Safe execution failed for {func.__name__}", e)
            return False, e
    
    def get_log_summary(self, hours: int = 24) -> dict:
        """Get summary of log entries from the last N hours."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            summary = {
                'info_count': 0,
                'warning_count': 0,
                'error_count': 0,
                'recent_errors': [],
                'last_activity': None
            }
            
            # Read main log file
            if self.log_file.exists():
                with open(self.log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            # Parse timestamp from log line
                            timestamp_str = line.split(' - ')[0]
                            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                            
                            if timestamp >= cutoff_time:
                                if ' - INFO - ' in line:
                                    summary['info_count'] += 1
                                elif ' - WARNING - ' in line:
                                    summary['warning_count'] += 1
                                elif ' - ERROR - ' in line:
                                    summary['error_count'] += 1
                                    summary['recent_errors'].append(line.strip())
                                
                                summary['last_activity'] = timestamp
                                
                        except (ValueError, IndexError):
                            continue
            
            return summary
            
        except Exception as e:
            self.log_error("Failed to generate log summary", e)
            return {}
    
    def cleanup_old_logs(self, days_to_keep: int = 30):
        """Clean up log files older than specified days."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            for log_file in self.log_dir.glob("*.log"):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    self.log_info(f"Deleted old log file: {log_file.name}")
            
        except Exception as e:
            self.log_error("Failed to cleanup old logs", e)


def with_error_handling(error_handler: ErrorHandler):
    """Decorator to add error handling to functions."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler.log_error(f"Error in {func.__name__}", e)
                raise
        return wrapper
    return decorator


def with_retry(error_handler: ErrorHandler, max_retries: int = 3):
    """Decorator to add retry mechanism to functions."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            return error_handler.retry_with_backoff(func, *args, max_retries=max_retries, **kwargs)
        return wrapper
    return decorator
