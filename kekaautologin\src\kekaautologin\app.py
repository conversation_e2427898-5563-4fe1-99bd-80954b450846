"""
Automated Keka attendance login app with background scheduling
"""

import toga
import asyncio
from datetime import datetime, time as dt_time
from toga.style import Pack
from toga.style.pack import COLUMN, ROW

from .credential_manager import <PERSON><PERSON><PERSON><PERSON>anager
from .scheduler import AttendanceScheduler
from .keka_automation import KekaAutomation


class KekaAutoLogin(toga.App):
    def startup(self):
        """Construct and show the Toga application."""
        # Initialize managers
        self.credential_manager = CredentialManager()
        self.scheduler = AttendanceScheduler(self.credential_manager)
        self.automation = None

        # Set up callbacks
        self.scheduler.set_callbacks(
            status_callback=self.update_status,
            error_callback=self.show_error
        )

        # State variables
        self.current_master_password = None
        self.is_logged_in = False

        # Create UI
        self.create_ui()

        # Check if credentials exist and show appropriate screen
        if self.credential_manager.credentials_exist():
            self.show_login_screen()
        else:
            self.show_setup_screen()

    def create_ui(self):
        """Create the main UI structure."""
        # Main container
        self.main_box = toga.Box(style=Pack(direction=COLUMN, padding=10))

        # Create different screens
        self.setup_screen = self.create_setup_screen()
        self.login_screen = self.create_login_screen()
        self.dashboard_screen = self.create_dashboard_screen()

        # Initially hide all screens
        self.setup_screen.style.display = 'none'
        self.login_screen.style.display = 'none'
        self.dashboard_screen.style.display = 'none'

        # Add screens to main box
        self.main_box.add(self.setup_screen)
        self.main_box.add(self.login_screen)
        self.main_box.add(self.dashboard_screen)

        # Create main window
        self.main_window = toga.MainWindow(title=self.formal_name)
        self.main_window.content = self.main_box
        self.main_window.show()

    def create_setup_screen(self):
        """Create initial setup screen for first-time users."""
        setup_box = toga.Box(style=Pack(direction=COLUMN, padding=20))

        # Title
        title = toga.Label(
            "Keka Auto Login Setup",
            style=Pack(text_align='center', font_size=18, padding_bottom=20)
        )
        setup_box.add(title)

        # Instructions
        instructions = toga.Label(
            "Enter your Keka credentials and a master password to secure them:",
            style=Pack(text_align='center', padding_bottom=20)
        )
        setup_box.add(instructions)

        # Keka username
        setup_box.add(toga.Label("Keka Username:", style=Pack(padding_bottom=5)))
        self.setup_username = toga.TextInput(
            placeholder="Enter your Keka username",
            style=Pack(width=300, padding_bottom=10)
        )
        setup_box.add(self.setup_username)

        # Keka password
        setup_box.add(toga.Label("Keka Password:", style=Pack(padding_bottom=5)))
        self.setup_password = toga.PasswordInput(
            placeholder="Enter your Keka password",
            style=Pack(width=300, padding_bottom=10)
        )
        setup_box.add(self.setup_password)

        # Master password
        setup_box.add(toga.Label("Master Password:", style=Pack(padding_bottom=5)))
        self.setup_master_password = toga.PasswordInput(
            placeholder="Create a master password",
            style=Pack(width=300, padding_bottom=10)
        )
        setup_box.add(self.setup_master_password)

        # Confirm master password
        setup_box.add(toga.Label("Confirm Master Password:", style=Pack(padding_bottom=5)))
        self.setup_confirm_password = toga.PasswordInput(
            placeholder="Confirm master password",
            style=Pack(width=300, padding_bottom=20)
        )
        setup_box.add(self.setup_confirm_password)

        # Save button
        save_button = toga.Button(
            "Save Credentials",
            on_press=self.save_credentials,
            style=Pack(width=200, padding=10)
        )
        setup_box.add(save_button)

        return setup_box

    def create_login_screen(self):
        """Create login screen for existing users."""
        login_box = toga.Box(style=Pack(direction=COLUMN, padding=20))

        # Title
        title = toga.Label(
            "Enter Master Password",
            style=Pack(text_align='center', font_size=18, padding_bottom=20)
        )
        login_box.add(title)

        # Master password input
        login_box.add(toga.Label("Master Password:", style=Pack(padding_bottom=5)))
        self.login_master_password = toga.PasswordInput(
            placeholder="Enter your master password",
            style=Pack(width=300, padding_bottom=20)
        )
        login_box.add(self.login_master_password)

        # Login button
        login_button = toga.Button(
            "Login",
            on_press=self.login_with_master_password,
            style=Pack(width=200, padding=10)
        )
        login_box.add(login_button)

        # Reset credentials button
        reset_button = toga.Button(
            "Reset Credentials",
            on_press=self.reset_credentials,
            style=Pack(width=200, padding=5)
        )
        login_box.add(reset_button)

        return login_box

    def create_dashboard_screen(self):
        """Create main dashboard screen."""
        dashboard_box = toga.Box(style=Pack(direction=COLUMN, padding=20))

        # Title
        title = toga.Label(
            "Keka Auto Login Dashboard",
            style=Pack(text_align='center', font_size=18, padding_bottom=20)
        )
        dashboard_box.add(title)

        # Status section
        status_section = toga.Box(style=Pack(direction=COLUMN, padding_bottom=20))
        status_section.add(toga.Label("Status:", style=Pack(font_weight='bold', padding_bottom=5)))

        self.status_label = toga.Label(
            "Ready",
            style=Pack(padding_bottom=10)
        )
        status_section.add(self.status_label)

        # Last activity
        self.last_activity_label = toga.Label(
            "Last Activity: None",
            style=Pack(padding_bottom=10)
        )
        status_section.add(self.last_activity_label)

        dashboard_box.add(status_section)

        # Manual controls section
        controls_section = toga.Box(style=Pack(direction=COLUMN, padding_bottom=20))
        controls_section.add(toga.Label("Manual Controls:", style=Pack(font_weight='bold', padding_bottom=10)))

        # Manual buttons row
        manual_buttons = toga.Box(style=Pack(direction=ROW, padding_bottom=10))

        self.clock_in_button = toga.Button(
            "Clock In Now",
            on_press=self.manual_clock_in,
            style=Pack(width=120, padding=5)
        )
        manual_buttons.add(self.clock_in_button)

        self.clock_out_button = toga.Button(
            "Clock Out Now",
            on_press=self.manual_clock_out,
            style=Pack(width=120, padding=5)
        )
        manual_buttons.add(self.clock_out_button)

        controls_section.add(manual_buttons)
        dashboard_box.add(controls_section)

        # Scheduler section
        scheduler_section = toga.Box(style=Pack(direction=COLUMN, padding_bottom=20))
        scheduler_section.add(toga.Label("Automatic Scheduling:", style=Pack(font_weight='bold', padding_bottom=10)))

        # Schedule status
        self.schedule_status_label = toga.Label(
            "Scheduler: Stopped",
            style=Pack(padding_bottom=10)
        )
        scheduler_section.add(self.schedule_status_label)

        # Scheduler controls
        scheduler_buttons = toga.Box(style=Pack(direction=ROW, padding_bottom=10))

        self.start_scheduler_button = toga.Button(
            "Start Scheduler",
            on_press=self.toggle_scheduler,
            style=Pack(width=120, padding=5)
        )
        scheduler_buttons.add(self.start_scheduler_button)

        scheduler_section.add(scheduler_buttons)
        dashboard_box.add(scheduler_section)

        # Settings section
        settings_section = toga.Box(style=Pack(direction=COLUMN))
        settings_section.add(toga.Label("Settings:", style=Pack(font_weight='bold', padding_bottom=10)))

        # Schedule times
        times_box = toga.Box(style=Pack(direction=ROW, padding_bottom=10))
        times_box.add(toga.Label("Clock In Time:", style=Pack(padding_right=10)))

        self.clock_in_time_input = toga.TextInput(
            value="10:00",
            style=Pack(width=80, padding_right=20)
        )
        times_box.add(self.clock_in_time_input)

        times_box.add(toga.Label("Clock Out Time:", style=Pack(padding_right=10)))

        self.clock_out_time_input = toga.TextInput(
            value="20:00",
            style=Pack(width=80)
        )
        times_box.add(self.clock_out_time_input)

        settings_section.add(times_box)

        # Update schedule button
        update_schedule_button = toga.Button(
            "Update Schedule",
            on_press=self.update_schedule_times,
            style=Pack(width=150, padding=10)
        )
        settings_section.add(update_schedule_button)

        dashboard_box.add(settings_section)

        return dashboard_box

    # Screen management methods
    def show_setup_screen(self):
        """Show the setup screen."""
        self.setup_screen.style.display = 'block'
        self.login_screen.style.display = 'none'
        self.dashboard_screen.style.display = 'none'

    def show_login_screen(self):
        """Show the login screen."""
        self.setup_screen.style.display = 'none'
        self.login_screen.style.display = 'block'
        self.dashboard_screen.style.display = 'none'

    def show_dashboard_screen(self):
        """Show the dashboard screen."""
        self.setup_screen.style.display = 'none'
        self.login_screen.style.display = 'none'
        self.dashboard_screen.style.display = 'block'
        self.update_dashboard_status()

    # Event handlers
    async def save_credentials(self, widget):
        """Save user credentials."""
        try:
            username = self.setup_username.value.strip()
            password = self.setup_password.value.strip()
            master_password = self.setup_master_password.value.strip()
            confirm_password = self.setup_confirm_password.value.strip()

            # Validation
            if not username or not password or not master_password:
                await self.main_window.info_dialog("Error", "Please fill in all fields.")
                return

            if master_password != confirm_password:
                await self.main_window.info_dialog("Error", "Master passwords do not match.")
                return

            if len(master_password) < 6:
                await self.main_window.info_dialog("Error", "Master password must be at least 6 characters long.")
                return

            # Save credentials
            if self.credential_manager.save_credentials(username, password, master_password):
                self.current_master_password = master_password
                self.is_logged_in = True
                await self.main_window.info_dialog("Success", "Credentials saved successfully!")
                self.show_dashboard_screen()
            else:
                await self.main_window.error_dialog("Error", "Failed to save credentials.")

        except Exception as e:
            await self.main_window.error_dialog("Error", f"An error occurred: {str(e)}")

    async def login_with_master_password(self, widget):
        """Login with master password."""
        try:
            master_password = self.login_master_password.value.strip()

            if not master_password:
                await self.main_window.info_dialog("Error", "Please enter your master password.")
                return

            # Verify master password
            if self.credential_manager.verify_master_password(master_password):
                self.current_master_password = master_password
                self.is_logged_in = True
                await self.main_window.info_dialog("Success", "Login successful!")
                self.show_dashboard_screen()
            else:
                await self.main_window.error_dialog("Error", "Invalid master password.")

        except Exception as e:
            await self.main_window.error_dialog("Error", f"An error occurred: {str(e)}")

    async def reset_credentials(self, widget):
        """Reset stored credentials."""
        try:
            result = await self.main_window.confirm_dialog(
                "Confirm Reset",
                "Are you sure you want to delete all stored credentials? This action cannot be undone."
            )

            if result:
                if self.credential_manager.delete_credentials():
                    await self.main_window.info_dialog("Success", "Credentials deleted successfully.")
                    self.current_master_password = None
                    self.is_logged_in = False
                    self.show_setup_screen()
                else:
                    await self.main_window.error_dialog("Error", "Failed to delete credentials.")

        except Exception as e:
            await self.main_window.error_dialog("Error", f"An error occurred: {str(e)}")

    async def manual_clock_in(self, widget):
        """Manually trigger clock-in."""
        try:
            if not self.current_master_password:
                await self.main_window.error_dialog("Error", "Please login first.")
                return

            self.update_status("Performing manual clock-in...")

            # Run in background to avoid blocking UI
            success = await asyncio.get_event_loop().run_in_executor(
                None, self.scheduler.manual_clock_in, self.current_master_password
            )

            if success:
                self.update_last_activity("Manual Clock-In")
                await self.main_window.info_dialog("Success", "Clock-in completed successfully!")
            else:
                await self.main_window.error_dialog("Error", "Clock-in failed. Check your credentials and internet connection.")

        except Exception as e:
            await self.main_window.error_dialog("Error", f"An error occurred: {str(e)}")
        finally:
            self.update_status("Ready")

    async def manual_clock_out(self, widget):
        """Manually trigger clock-out."""
        try:
            if not self.current_master_password:
                await self.main_window.error_dialog("Error", "Please login first.")
                return

            self.update_status("Performing manual clock-out...")

            # Run in background to avoid blocking UI
            success = await asyncio.get_event_loop().run_in_executor(
                None, self.scheduler.manual_clock_out, self.current_master_password
            )

            if success:
                self.update_last_activity("Manual Clock-Out")
                await self.main_window.info_dialog("Success", "Clock-out completed successfully!")
            else:
                await self.main_window.error_dialog("Error", "Clock-out failed. Check your credentials and internet connection.")

        except Exception as e:
            await self.main_window.error_dialog("Error", f"An error occurred: {str(e)}")
        finally:
            self.update_status("Ready")

    async def toggle_scheduler(self, widget):
        """Toggle the background scheduler."""
        try:
            if not self.current_master_password:
                await self.main_window.error_dialog("Error", "Please login first.")
                return

            if self.scheduler.is_running():
                self.scheduler.stop_scheduler()
                self.start_scheduler_button.text = "Start Scheduler"
                await self.main_window.info_dialog("Info", "Scheduler stopped.")
            else:
                self.scheduler.start_scheduler(self.current_master_password)
                self.start_scheduler_button.text = "Stop Scheduler"
                await self.main_window.info_dialog("Info", "Scheduler started.")

            self.update_dashboard_status()

        except Exception as e:
            await self.main_window.error_dialog("Error", f"An error occurred: {str(e)}")

    async def update_schedule_times(self, widget):
        """Update schedule times."""
        try:
            clock_in_str = self.clock_in_time_input.value.strip()
            clock_out_str = self.clock_out_time_input.value.strip()

            # Parse time strings
            try:
                clock_in_time = dt_time.fromisoformat(clock_in_str)
                clock_out_time = dt_time.fromisoformat(clock_out_str)
            except ValueError:
                await self.main_window.error_dialog("Error", "Invalid time format. Use HH:MM format (e.g., 10:00).")
                return

            # Update scheduler
            self.scheduler.set_schedule_times(clock_in_time, clock_out_time)

            # Restart scheduler if it's running
            if self.scheduler.is_running() and self.current_master_password:
                self.scheduler.stop_scheduler()
                self.scheduler.start_scheduler(self.current_master_password)

            await self.main_window.info_dialog("Success", "Schedule times updated successfully!")
            self.update_dashboard_status()

        except Exception as e:
            await self.main_window.error_dialog("Error", f"An error occurred: {str(e)}")

    # Status update methods
    def update_status(self, message):
        """Update status label."""
        if hasattr(self, 'status_label'):
            self.status_label.text = message

    def update_last_activity(self, activity):
        """Update last activity label."""
        if hasattr(self, 'last_activity_label'):
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.last_activity_label.text = f"Last Activity: {activity} at {timestamp}"

    def update_dashboard_status(self):
        """Update dashboard status information."""
        if hasattr(self, 'schedule_status_label'):
            status = self.scheduler.get_status()
            self.schedule_status_label.text = f"Scheduler: {status}"

        if hasattr(self, 'start_scheduler_button'):
            if self.scheduler.is_running():
                self.start_scheduler_button.text = "Stop Scheduler"
            else:
                self.start_scheduler_button.text = "Start Scheduler"

    def show_error(self, error_message):
        """Show error message (callback from scheduler)."""
        # This will be called from background thread, so we need to be careful
        # In a real implementation, you might want to queue this for the main thread
        print(f"Scheduler Error: {error_message}")

    def on_exit(self):
        """Clean up when app exits."""
        if self.scheduler:
            self.scheduler.stop_scheduler()
        if self.automation:
            self.automation.close()


def main():
    return KekaAutoLogin()
