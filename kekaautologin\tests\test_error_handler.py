"""
Unit tests for <PERSON>rror<PERSON>and<PERSON> module.
Tests logging, retry mechanisms, and network connectivity checks.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
import sys
import os
import time

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from kekaautologin.error_handler import <PERSON>rror<PERSON>and<PERSON>, with_retry, with_error_handling


class TestErrorHandler(unittest.TestCase):
    """Test cases for ErrorHandler."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test logs
        self.test_dir = tempfile.mkdtemp()
        
        # Create error handler with test directory
        self.error_handler = ErrorHandler("TestApp")
        self.error_handler.log_dir = Path(self.test_dir)
        self.error_handler.log_file = self.error_handler.log_dir / "test.log"
        self.error_handler.error_log_file = self.error_handler.log_dir / "test_errors.log"
        
        # Recreate loggers with test paths
        self.error_handler.logger = self.error_handler._setup_main_logger()
        self.error_handler.error_logger = self.error_handler._setup_error_logger()
    
    def tearDown(self):
        """Clean up test environment."""
        # Remove temporary directory
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_log_info(self):
        """Test info logging."""
        test_message = "Test info message"
        self.error_handler.log_info(test_message)
        
        # Check if log file was created and contains message
        self.assertTrue(self.error_handler.log_file.exists())
        
        with open(self.error_handler.log_file, 'r') as f:
            content = f.read()
            self.assertIn(test_message, content)
            self.assertIn("INFO", content)
    
    def test_log_error(self):
        """Test error logging."""
        test_message = "Test error message"
        test_exception = ValueError("Test exception")
        
        self.error_handler.log_error(test_message, test_exception)
        
        # Check error log file
        self.assertTrue(self.error_handler.error_log_file.exists())
        
        with open(self.error_handler.error_log_file, 'r') as f:
            content = f.read()
            self.assertIn(test_message, content)
            self.assertIn("ERROR", content)
            self.assertIn("Test exception", content)
    
    @patch('requests.get')
    def test_network_connectivity_success(self, mock_get):
        """Test successful network connectivity check."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        result = self.error_handler.check_network_connectivity()
        self.assertTrue(result)
    
    @patch('requests.get')
    def test_network_connectivity_failure(self, mock_get):
        """Test failed network connectivity check."""
        # Mock failed response
        mock_get.side_effect = Exception("Network error")
        
        result = self.error_handler.check_network_connectivity()
        self.assertFalse(result)
    
    def test_retry_with_backoff_success(self):
        """Test retry mechanism with successful function."""
        # Function that succeeds on first try
        mock_func = Mock(return_value="success")
        
        result = self.error_handler.retry_with_backoff(mock_func, "arg1", kwarg1="value1")
        
        self.assertEqual(result, "success")
        mock_func.assert_called_once_with("arg1", kwarg1="value1")
    
    def test_retry_with_backoff_eventual_success(self):
        """Test retry mechanism with eventual success."""
        # Function that fails twice then succeeds
        mock_func = Mock(side_effect=[Exception("Error 1"), Exception("Error 2"), "success"])
        
        result = self.error_handler.retry_with_backoff(mock_func, max_retries=3, retry_delay=0.1)
        
        self.assertEqual(result, "success")
        self.assertEqual(mock_func.call_count, 3)
    
    def test_retry_with_backoff_total_failure(self):
        """Test retry mechanism with total failure."""
        # Function that always fails
        mock_func = Mock(side_effect=Exception("Always fails"))
        
        with self.assertRaises(Exception):
            self.error_handler.retry_with_backoff(mock_func, max_retries=2, retry_delay=0.1)
        
        self.assertEqual(mock_func.call_count, 3)  # Initial + 2 retries
    
    def test_safe_execute_success(self):
        """Test safe execution with successful function."""
        def success_func():
            return "success"
        
        success, result = self.error_handler.safe_execute(success_func)
        
        self.assertTrue(success)
        self.assertEqual(result, "success")
    
    def test_safe_execute_failure(self):
        """Test safe execution with failing function."""
        def failing_func():
            raise ValueError("Test error")
        
        success, result = self.error_handler.safe_execute(failing_func)
        
        self.assertFalse(success)
        self.assertIsInstance(result, ValueError)
    
    def test_with_error_handling_decorator(self):
        """Test error handling decorator."""
        @with_error_handling(self.error_handler)
        def test_function():
            raise ValueError("Decorated function error")
        
        with self.assertRaises(ValueError):
            test_function()
        
        # Check that error was logged
        self.assertTrue(self.error_handler.error_log_file.exists())
    
    def test_with_retry_decorator(self):
        """Test retry decorator."""
        call_count = 0
        
        @with_retry(self.error_handler, max_retries=2)
        def test_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ValueError(f"Attempt {call_count}")
            return "success"
        
        # Should succeed on third attempt
        result = test_function()
        self.assertEqual(result, "success")
        self.assertEqual(call_count, 3)
    
    def test_log_summary(self):
        """Test log summary generation."""
        # Generate some log entries
        self.error_handler.log_info("Info message 1")
        self.error_handler.log_info("Info message 2")
        self.error_handler.log_warning("Warning message")
        self.error_handler.log_error("Error message")
        
        # Get summary
        summary = self.error_handler.get_log_summary(hours=1)
        
        # Verify summary contains expected counts
        self.assertIsInstance(summary, dict)
        self.assertIn('info_count', summary)
        self.assertIn('warning_count', summary)
        self.assertIn('error_count', summary)
        
        # Should have at least some entries
        self.assertGreater(summary['info_count'], 0)
        self.assertGreater(summary['error_count'], 0)
    
    @patch('time.sleep')  # Mock sleep to speed up test
    @patch('requests.get')
    def test_wait_for_network(self, mock_get, mock_sleep):
        """Test waiting for network connectivity."""
        # First call fails, second succeeds
        mock_response = Mock()
        mock_response.status_code = 200
        mock_get.side_effect = [Exception("Network error"), mock_response]
        
        result = self.error_handler.wait_for_network(max_wait_time=60)
        self.assertTrue(result)
    
    def test_cleanup_old_logs(self):
        """Test cleanup of old log files."""
        # Create some test log files
        old_log = self.error_handler.log_dir / "old.log"
        recent_log = self.error_handler.log_dir / "recent.log"
        
        old_log.touch()
        recent_log.touch()
        
        # Modify timestamps to simulate old file
        old_time = time.time() - (40 * 24 * 60 * 60)  # 40 days ago
        os.utime(old_log, (old_time, old_time))
        
        # Cleanup logs older than 30 days
        self.error_handler.cleanup_old_logs(days_to_keep=30)
        
        # Old log should be deleted, recent should remain
        self.assertFalse(old_log.exists())
        self.assertTrue(recent_log.exists())


if __name__ == '__main__':
    unittest.main()
