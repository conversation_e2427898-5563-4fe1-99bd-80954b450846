# This project was generated with 0.3.24 using template: https://github.com/beeware/briefcase-template @ v0.3.24
[tool.briefcase]
project_name = "Keka Auto Login"
bundle = "com.kekaautologin"
version = "0.0.1"
url = "https://kekaautologin.com/kekaautologin"
license.file = "LICENSE"
author = "Vinay L"
author_email = "<EMAIL>"

[tool.briefcase.app.kekaautologin]
formal_name = "Keka Auto Login"
description = "Automated Keka attendance login app with background scheduling"
long_description = """More details about the app should go here.
"""
sources = [
    "src/kekaautologin",
]
test_sources = [
    "tests",
]

requires = [
    "selenium>=4.15.0",
    "cryptography>=41.0.0",
    "schedule>=1.2.0",
    "requests>=2.31.0",
    "python-dateutil>=2.8.2",
]
test_requires = [
    "pytest",
]

[tool.briefcase.app.kekaautologin.macOS]
universal_build = true
requires = [
    "toga-cocoa~=0.5.0",
    "std-nslog~=1.0.3",
]

[tool.briefcase.app.kekaautologin.linux]
requires = [
    "toga-gtk~=0.5.0",
    # PyGObject 3.52.1 enforces a requirement on libgirepository-2.0-dev. This library
    # isn't available on Debian 12/Ubuntu 22.04. If you don't need to support those (or
    # older) releases, you can remove this version pin. See beeware/toga#3143.
    "pygobject < 3.52.1",
]

[tool.briefcase.app.kekaautologin.linux.system.debian]
system_requires = [
    # Needed to compile pycairo wheel
    "libcairo2-dev",
    # One of the following two packages are needed to compile PyGObject wheel. If you
    # remove the pygobject pin in the requires list, you should also change to the
    # version 2.0 of the girepository library. See beeware/toga#3143.
    "libgirepository1.0-dev",
    # "libgirepository-2.0-dev",
]

system_runtime_requires = [
    # Needed to provide GTK and its GI bindings
    "gir1.2-gtk-3.0",
    # One of the following two packages are needed to use PyGObject at runtime. If you
    # remove the pygobject pin in the requires list, you should also change to the
    # version 2.0 of the girepository library. See beeware/toga#3143.
    "libgirepository-1.0-1",
    # "libgirepository-2.0-0",
    # Dependencies that GTK looks for at runtime
    "libcanberra-gtk3-module",
    # Needed to provide WebKit2 at runtime
    # Note: Debian 11 requires gir1.2-webkit2-4.0 instead
    # "gir1.2-webkit2-4.1",
]

[tool.briefcase.app.kekaautologin.linux.system.rhel]
system_requires = [
    # Needed to compile pycairo wheel
    "cairo-gobject-devel",
    # Needed to compile PyGObject wheel
    "gobject-introspection-devel",
]

system_runtime_requires = [
    # Needed to support Python bindings to GTK
    "gobject-introspection",
    # Needed to provide GTK
    "gtk3",
    # Dependencies that GTK looks for at runtime
    "libcanberra-gtk3",
    # Needed to provide WebKit2 at runtime
    # "webkit2gtk3",
]

[tool.briefcase.app.kekaautologin.linux.system.suse]
system_requires = [
    # Needed to compile pycairo wheel
    "cairo-devel",
    # Needed to compile PyGObject wheel
    "gobject-introspection-devel",
]

system_runtime_requires = [
    # Needed to provide GTK
    "gtk3",
    # Needed to support Python bindings to GTK
    "gobject-introspection", "typelib(Gtk) = 3.0",
    # Dependencies that GTK looks for at runtime
    "libcanberra-gtk3-module",
    # Needed to provide WebKit2 at runtime
    # "libwebkit2gtk3", "typelib(WebKit2)",
]

[tool.briefcase.app.kekaautologin.linux.system.arch]
system_requires = [
    # Needed to compile pycairo wheel
    "cairo",
    # Needed to compile PyGObject wheel
    "gobject-introspection",
    # Runtime dependencies that need to exist so that the
    # Arch package passes final validation.
    # Needed to provide GTK
    "gtk3",
    # Dependencies that GTK looks for at runtime
    "libcanberra",
    # Needed to provide WebKit2
    # "webkit2gtk",
]

system_runtime_requires = [
    # Needed to provide GTK
    "gtk3",
    # Needed to provide PyGObject bindings
    "gobject-introspection-runtime",
    # Dependencies that GTK looks for at runtime
    "libcanberra",
    # Needed to provide WebKit2 at runtime
    # "webkit2gtk",
]

[tool.briefcase.app.kekaautologin.linux.appimage]
manylinux = "manylinux_2_28"

system_requires = [
    # Needed to compile pycairo wheel
    "cairo-gobject-devel",
    # Needed to compile PyGObject wheel
    "gobject-introspection-devel",
    # Needed to provide GTK
    "gtk3-devel",
    # Dependencies that GTK looks for at runtime, that need to be
    # in the build environment to be picked up by linuxdeploy
    "libcanberra-gtk3",
    "PackageKit-gtk3-module",
    "gvfs-client",
]

linuxdeploy_plugins = [
    "DEPLOY_GTK_VERSION=3 gtk",
]

[tool.briefcase.app.kekaautologin.linux.flatpak]
flatpak_runtime = "org.gnome.Platform"
flatpak_runtime_version = "48"
flatpak_sdk = "org.gnome.Sdk"

[tool.briefcase.app.kekaautologin.windows]
requires = [
    "toga-winforms~=0.5.0",
]

# Mobile deployments
[tool.briefcase.app.kekaautologin.iOS]
requires = [
    "toga-iOS~=0.5.0",
    "std-nslog~=1.0.3",
]

[tool.briefcase.app.kekaautologin.android]
requires = [
    "toga-android~=0.5.0",
]

base_theme = "Theme.MaterialComponents.Light.DarkActionBar"

build_gradle_dependencies = [
    "com.google.android.material:material:1.12.0",
    # Needed for DetailedList
    # "androidx.swiperefreshlayout:swiperefreshlayout:1.1.0",
    # Needed for MapView
    # "org.osmdroid:osmdroid-android:6.1.20",
]

# Web deployments
[tool.briefcase.app.kekaautologin.web]
requires = [
    "toga-web~=0.5.0",
]
style_framework = "Shoelace v2.3"

