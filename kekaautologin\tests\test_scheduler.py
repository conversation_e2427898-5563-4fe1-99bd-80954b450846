"""
Unit tests for AttendanceScheduler module.
Tests scheduling functionality, manual operations, and error handling.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
from datetime import time as dt_time
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from kekaautologin.scheduler import AttendanceScheduler
from kekaautologin.credential_manager import CredentialManager


class TestAttendanceScheduler(unittest.TestCase):
    """Test cases for AttendanceScheduler."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test credentials
        self.test_dir = tempfile.mkdtemp()
        
        # Create credential manager with test directory
        self.credential_manager = CredentialManager()
        self.credential_manager.config_dir = Path(self.test_dir)
        self.credential_manager.credentials_file = self.credential_manager.config_dir / "credentials.enc"
        
        # Save test credentials
        self.test_username = "test_user"
        self.test_password = "test_password"
        self.test_master_password = "master123"
        self.credential_manager.save_credentials(
            self.test_username, self.test_password, self.test_master_password
        )
        
        # Create scheduler
        self.scheduler = AttendanceScheduler(self.credential_manager)
        
        # Mock callbacks
        self.status_callback = Mock()
        self.error_callback = Mock()
        self.scheduler.set_callbacks(self.status_callback, self.error_callback)
    
    def tearDown(self):
        """Clean up test environment."""
        # Stop scheduler if running
        if self.scheduler.is_running():
            self.scheduler.stop_scheduler()
        
        # Remove temporary directory
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_schedule_times_configuration(self):
        """Test setting and getting schedule times."""
        clock_in_time = dt_time(9, 30)
        clock_out_time = dt_time(18, 30)
        
        self.scheduler.set_schedule_times(clock_in_time, clock_out_time)
        
        self.assertEqual(self.scheduler.clock_in_time, clock_in_time)
        self.assertEqual(self.scheduler.clock_out_time, clock_out_time)
    
    def test_scheduler_start_stop(self):
        """Test starting and stopping the scheduler."""
        # Initially not running
        self.assertFalse(self.scheduler.is_running())
        
        # Start scheduler
        self.scheduler.start_scheduler(self.test_master_password)
        self.assertTrue(self.scheduler.is_running())
        
        # Stop scheduler
        self.scheduler.stop_scheduler()
        self.assertFalse(self.scheduler.is_running())
    
    def test_scheduler_status(self):
        """Test scheduler status reporting."""
        # Initially stopped
        status = self.scheduler.get_status()
        self.assertIn("Stopped", status)
        
        # Start scheduler
        self.scheduler.start_scheduler(self.test_master_password)
        status = self.scheduler.get_status()
        self.assertIn("Running", status)
    
    @patch('kekaautologin.scheduler.KekaAutomation')
    def test_manual_clock_in_success(self, mock_automation_class):
        """Test successful manual clock-in operation."""
        # Mock automation
        mock_automation = Mock()
        mock_automation.login.return_value = True
        mock_automation.clock_in.return_value = True
        mock_automation_class.return_value = mock_automation
        
        # Perform manual clock-in
        result = self.scheduler.manual_clock_in(self.test_master_password)
        
        # Verify success
        self.assertTrue(result)
        mock_automation.login.assert_called_once_with(self.test_username, self.test_password)
        mock_automation.clock_in.assert_called_once()
    
    @patch('kekaautologin.scheduler.KekaAutomation')
    def test_manual_clock_in_login_failure(self, mock_automation_class):
        """Test manual clock-in with login failure."""
        # Mock automation with login failure
        mock_automation = Mock()
        mock_automation.login.return_value = False
        mock_automation_class.return_value = mock_automation
        
        # Perform manual clock-in
        result = self.scheduler.manual_clock_in(self.test_master_password)
        
        # Verify failure
        self.assertFalse(result)
        mock_automation.login.assert_called_once()
        mock_automation.clock_in.assert_not_called()
    
    @patch('kekaautologin.scheduler.KekaAutomation')
    def test_manual_clock_out_success(self, mock_automation_class):
        """Test successful manual clock-out operation."""
        # Mock automation
        mock_automation = Mock()
        mock_automation.login.return_value = True
        mock_automation.clock_out.return_value = True
        mock_automation_class.return_value = mock_automation
        
        # Perform manual clock-out
        result = self.scheduler.manual_clock_out(self.test_master_password)
        
        # Verify success
        self.assertTrue(result)
        mock_automation.login.assert_called_once_with(self.test_username, self.test_password)
        mock_automation.clock_out.assert_called_once()
    
    def test_invalid_master_password(self):
        """Test operations with invalid master password."""
        invalid_password = "wrong_password"
        
        # Manual clock-in should fail
        result = self.scheduler.manual_clock_in(invalid_password)
        self.assertFalse(result)
        
        # Manual clock-out should fail
        result = self.scheduler.manual_clock_out(invalid_password)
        self.assertFalse(result)
    
    def test_callback_notifications(self):
        """Test that callbacks are properly called."""
        # Reset mocks
        self.status_callback.reset_mock()
        self.error_callback.reset_mock()
        
        # Trigger an operation that should call callbacks
        with patch('kekaautologin.scheduler.KekaAutomation') as mock_automation_class:
            mock_automation = Mock()
            mock_automation.login.return_value = True
            mock_automation.clock_in.return_value = True
            mock_automation_class.return_value = mock_automation
            
            self.scheduler.manual_clock_in(self.test_master_password)
        
        # Verify status callback was called
        self.assertTrue(self.status_callback.called)
    
    @patch('kekaautologin.scheduler.KekaAutomation')
    def test_automation_exception_handling(self, mock_automation_class):
        """Test handling of exceptions during automation."""
        # Mock automation that raises exception
        mock_automation = Mock()
        mock_automation.login.side_effect = Exception("Network error")
        mock_automation_class.return_value = mock_automation
        
        # Perform operation
        result = self.scheduler.manual_clock_in(self.test_master_password)
        
        # Should handle exception gracefully
        self.assertFalse(result)
    
    def test_multiple_scheduler_starts(self):
        """Test that starting scheduler multiple times doesn't cause issues."""
        # Start scheduler multiple times
        self.scheduler.start_scheduler(self.test_master_password)
        self.assertTrue(self.scheduler.is_running())
        
        # Starting again should not cause issues
        self.scheduler.start_scheduler(self.test_master_password)
        self.assertTrue(self.scheduler.is_running())
        
        # Should still be able to stop
        self.scheduler.stop_scheduler()
        self.assertFalse(self.scheduler.is_running())
    
    def test_schedule_time_validation(self):
        """Test validation of schedule times."""
        # Valid times should work
        valid_clock_in = dt_time(9, 0)
        valid_clock_out = dt_time(17, 0)
        
        try:
            self.scheduler.set_schedule_times(valid_clock_in, valid_clock_out)
        except Exception as e:
            self.fail(f"Valid times should not raise exception: {e}")
        
        # Verify times were set
        self.assertEqual(self.scheduler.clock_in_time, valid_clock_in)
        self.assertEqual(self.scheduler.clock_out_time, valid_clock_out)


if __name__ == '__main__':
    unittest.main()
