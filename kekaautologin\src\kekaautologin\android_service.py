"""
Android-specific background service implementation for Keka Auto Login.
Handles foreground service, WorkManager integration, and Android permissions.
"""

import os
import sys
import logging
from datetime import datetime, time as dt_time

# Android-specific imports (will be available when running on Android)
try:
    from android.permissions import request_permissions, Permission
    from android.broadcast import BroadcastReceiver
    from jnius import autoclass, PythonJavaClass, java_method
    
    # Android Java classes
    PythonService = autoclass('org.kivy.android.PythonService')
    Context = autoclass('android.content.Context')
    Intent = autoclass('android.content.Intent')
    PendingIntent = autoclass('android.app.PendingIntent')
    AlarmManager = autoclass('android.app.AlarmManager')
    Calendar = autoclass('java.util.Calendar')
    NotificationManager = autoclass('android.app.NotificationManager')
    NotificationChannel = autoclass('android.app.NotificationChannel')
    NotificationCompat = autoclass('androidx.core.app.NotificationCompat')
    
    ANDROID_AVAILABLE = True
except ImportError:
    ANDROID_AVAILABLE = False
    print("Android modules not available - running in development mode")


class AndroidBackgroundService:
    """Manages Android background service for automated attendance."""
    
    def __init__(self, credential_manager, scheduler):
        self.credential_manager = credential_manager
        self.scheduler = scheduler
        self.logger = self._setup_logger()
        self.service = None
        self.notification_id = 1001
        self.channel_id = "keka_auto_login_channel"
        
    def _setup_logger(self):
        """Setup logging for Android service."""
        logger = logging.getLogger('AndroidService')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def request_permissions(self):
        """Request necessary Android permissions."""
        if not ANDROID_AVAILABLE:
            self.logger.info("Android not available - skipping permission requests")
            return True
        
        try:
            # Request permissions for background execution, alarms, and notifications
            permissions = [
                Permission.WAKE_LOCK,
                Permission.RECEIVE_BOOT_COMPLETED,
                Permission.SCHEDULE_EXACT_ALARM,
                Permission.USE_EXACT_ALARM,
                Permission.FOREGROUND_SERVICE,
                Permission.POST_NOTIFICATIONS,
                Permission.INTERNET,
                Permission.ACCESS_NETWORK_STATE,
            ]
            
            request_permissions(permissions)
            self.logger.info("Permissions requested successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to request permissions: {e}")
            return False
    
    def create_notification_channel(self):
        """Create notification channel for foreground service."""
        if not ANDROID_AVAILABLE:
            return
        
        try:
            from jnius import autoclass
            
            # Get the notification manager
            context = autoclass('org.kivy.android.PythonActivity').mActivity
            notification_manager = context.getSystemService(Context.NOTIFICATION_SERVICE)
            
            # Create notification channel (required for Android 8.0+)
            if hasattr(NotificationChannel, '__init__'):
                channel = NotificationChannel(
                    self.channel_id,
                    "Keka Auto Login Service",
                    NotificationManager.IMPORTANCE_LOW
                )
                channel.setDescription("Background service for automated Keka attendance")
                notification_manager.createNotificationChannel(channel)
                
                self.logger.info("Notification channel created")
            
        except Exception as e:
            self.logger.error(f"Failed to create notification channel: {e}")
    
    def create_foreground_notification(self):
        """Create notification for foreground service."""
        if not ANDROID_AVAILABLE:
            return None
        
        try:
            from jnius import autoclass
            
            context = autoclass('org.kivy.android.PythonActivity').mActivity
            
            # Create notification
            builder = NotificationCompat.Builder(context, self.channel_id)
            builder.setContentTitle("Keka Auto Login")
            builder.setContentText("Background service running")
            builder.setSmallIcon(android.R.drawable.ic_dialog_info)
            builder.setPriority(NotificationCompat.PRIORITY_LOW)
            builder.setOngoing(True)
            
            # Create intent for when notification is tapped
            intent = Intent(context, autoclass('org.kivy.android.PythonActivity'))
            pending_intent = PendingIntent.getActivity(
                context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT
            )
            builder.setContentIntent(pending_intent)
            
            return builder.build()
            
        except Exception as e:
            self.logger.error(f"Failed to create notification: {e}")
            return None
    
    def start_foreground_service(self):
        """Start foreground service for background execution."""
        if not ANDROID_AVAILABLE:
            self.logger.info("Android not available - using regular scheduler")
            return True
        
        try:
            # Create notification channel
            self.create_notification_channel()
            
            # Create notification
            notification = self.create_foreground_notification()
            if not notification:
                return False
            
            # Start foreground service
            from jnius import autoclass
            service = autoclass('org.kivy.android.PythonService')
            service.start(autoclass('org.kivy.android.PythonActivity').mActivity, '')
            
            # Make service foreground
            service.startForeground(self.notification_id, notification)
            
            self.logger.info("Foreground service started")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start foreground service: {e}")
            return False
    
    def schedule_alarms(self, clock_in_time: dt_time, clock_out_time: dt_time):
        """Schedule alarms using Android AlarmManager."""
        if not ANDROID_AVAILABLE:
            self.logger.info("Android not available - using Python scheduler")
            return True
        
        try:
            from jnius import autoclass
            
            context = autoclass('org.kivy.android.PythonActivity').mActivity
            alarm_manager = context.getSystemService(Context.ALARM_SERVICE)
            
            # Create intents for clock-in and clock-out
            clock_in_intent = Intent(context, autoclass('KekaClockInReceiver'))
            clock_out_intent = Intent(context, autoclass('KekaClockOutReceiver'))
            
            # Create pending intents
            clock_in_pending = PendingIntent.getBroadcast(
                context, 1001, clock_in_intent, PendingIntent.FLAG_UPDATE_CURRENT
            )
            clock_out_pending = PendingIntent.getBroadcast(
                context, 1002, clock_out_intent, PendingIntent.FLAG_UPDATE_CURRENT
            )
            
            # Calculate next alarm times
            now = datetime.now()
            today = now.date()
            
            # Clock-in alarm
            clock_in_datetime = datetime.combine(today, clock_in_time)
            if clock_in_datetime <= now:
                clock_in_datetime = clock_in_datetime.replace(day=today.day + 1)
            
            # Clock-out alarm
            clock_out_datetime = datetime.combine(today, clock_out_time)
            if clock_out_datetime <= now:
                clock_out_datetime = clock_out_datetime.replace(day=today.day + 1)
            
            # Set repeating alarms
            alarm_manager.setRepeating(
                AlarmManager.RTC_WAKEUP,
                int(clock_in_datetime.timestamp() * 1000),
                AlarmManager.INTERVAL_DAY,
                clock_in_pending
            )
            
            alarm_manager.setRepeating(
                AlarmManager.RTC_WAKEUP,
                int(clock_out_datetime.timestamp() * 1000),
                AlarmManager.INTERVAL_DAY,
                clock_out_pending
            )
            
            self.logger.info(f"Alarms scheduled: Clock-in at {clock_in_time}, Clock-out at {clock_out_time}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to schedule alarms: {e}")
            return False
    
    def cancel_alarms(self):
        """Cancel scheduled alarms."""
        if not ANDROID_AVAILABLE:
            return True
        
        try:
            from jnius import autoclass
            
            context = autoclass('org.kivy.android.PythonActivity').mActivity
            alarm_manager = context.getSystemService(Context.ALARM_SERVICE)
            
            # Cancel clock-in alarm
            clock_in_intent = Intent(context, autoclass('KekaClockInReceiver'))
            clock_in_pending = PendingIntent.getBroadcast(
                context, 1001, clock_in_intent, PendingIntent.FLAG_UPDATE_CURRENT
            )
            alarm_manager.cancel(clock_in_pending)
            
            # Cancel clock-out alarm
            clock_out_intent = Intent(context, autoclass('KekaClockOutReceiver'))
            clock_out_pending = PendingIntent.getBroadcast(
                context, 1002, clock_out_intent, PendingIntent.FLAG_UPDATE_CURRENT
            )
            alarm_manager.cancel(clock_out_pending)
            
            self.logger.info("Alarms cancelled")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel alarms: {e}")
            return False
    
    def stop_foreground_service(self):
        """Stop foreground service."""
        if not ANDROID_AVAILABLE:
            return True
        
        try:
            from jnius import autoclass
            service = autoclass('org.kivy.android.PythonService')
            service.stop(autoclass('org.kivy.android.PythonActivity').mActivity)
            
            self.logger.info("Foreground service stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop foreground service: {e}")
            return False


class KekaAlarmReceiver(BroadcastReceiver):
    """Broadcast receiver for handling scheduled alarms."""
    
    def __init__(self, action_type):
        super().__init__()
        self.action_type = action_type  # 'clock_in' or 'clock_out'
    
    def onReceive(self, context, intent):
        """Handle alarm broadcast."""
        try:
            # This would trigger the actual clock-in/clock-out operation
            # In a real implementation, you'd need to:
            # 1. Load credentials from secure storage
            # 2. Perform the automation task
            # 3. Handle errors and retries
            
            if self.action_type == 'clock_in':
                self._perform_clock_in()
            elif self.action_type == 'clock_out':
                self._perform_clock_out()
                
        except Exception as e:
            print(f"Error in alarm receiver: {e}")
    
    def _perform_clock_in(self):
        """Perform clock-in operation from alarm."""
        # This would be implemented to trigger the actual automation
        print("Alarm triggered: Clock-in")
    
    def _perform_clock_out(self):
        """Perform clock-out operation from alarm."""
        # This would be implemented to trigger the actual automation
        print("Alarm triggered: Clock-out")
