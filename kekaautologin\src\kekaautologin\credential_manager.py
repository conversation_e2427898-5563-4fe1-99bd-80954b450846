"""
Secure credential storage and management for Keka Auto Login app.
Handles encryption/decryption of user credentials using AES encryption.
"""

import os
import json
import base64
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class CredentialManager:
    """Manages secure storage and retrieval of user credentials."""
    
    def __init__(self, app_name="KekaAutoLogin"):
        self.app_name = app_name
        self.config_dir = self._get_config_directory()
        self.credentials_file = self.config_dir / "credentials.enc"
        self.salt_file = self.config_dir / "salt.key"
        
        # Ensure config directory exists
        self.config_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_config_directory(self):
        """Get platform-specific configuration directory."""
        if os.name == 'nt':  # Windows
            config_dir = Path(os.environ.get('APPDATA', '')) / self.app_name
        elif os.name == 'posix':  # Unix/Linux/macOS
            config_dir = Path.home() / '.config' / self.app_name
        else:
            config_dir = Path.home() / f'.{self.app_name}'
        
        return config_dir
    
    def _generate_key(self, password: str) -> bytes:
        """Generate encryption key from password using PBKDF2."""
        # Load or generate salt
        if self.salt_file.exists():
            with open(self.salt_file, 'rb') as f:
                salt = f.read()
        else:
            salt = os.urandom(16)
            with open(self.salt_file, 'wb') as f:
                f.write(salt)
        
        # Derive key from password
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key
    
    def save_credentials(self, username: str, password: str, master_password: str) -> bool:
        """
        Save encrypted credentials to file.

        Args:
            username: Keka username
            password: Keka password
            master_password: Master password for encryption

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Validate inputs
            if not username or not username.strip():
                return False
            if not password or not password.strip():
                return False
            if not master_password or not master_password.strip():
                return False

            # Generate encryption key
            key = self._generate_key(master_password)
            fernet = Fernet(key)
            
            # Prepare credentials data
            credentials_data = {
                'username': username,
                'password': password,
                'app_version': '1.0.0'
            }
            
            # Encrypt and save
            encrypted_data = fernet.encrypt(json.dumps(credentials_data).encode())
            with open(self.credentials_file, 'wb') as f:
                f.write(encrypted_data)
            
            return True
        except Exception as e:
            print(f"Error saving credentials: {e}")
            return False
    
    def load_credentials(self, master_password: str) -> dict:
        """
        Load and decrypt credentials from file.

        Args:
            master_password: Master password for decryption

        Returns:
            dict: Credentials data or None if failed/wrong password
        """
        try:
            if not self.credentials_file.exists():
                return None

            # Generate decryption key
            key = self._generate_key(master_password)
            fernet = Fernet(key)

            # Load and decrypt
            with open(self.credentials_file, 'rb') as f:
                encrypted_data = f.read()

            decrypted_data = fernet.decrypt(encrypted_data)
            credentials = json.loads(decrypted_data.decode())

            return credentials
        except Exception as e:
            print(f"Error loading credentials: {e}")
            return None
    
    def credentials_exist(self) -> bool:
        """Check if credentials file exists."""
        return self.credentials_file.exists()
    
    def delete_credentials(self) -> bool:
        """Delete stored credentials."""
        try:
            if self.credentials_file.exists():
                self.credentials_file.unlink()
            if self.salt_file.exists():
                self.salt_file.unlink()
            return True
        except Exception as e:
            print(f"Error deleting credentials: {e}")
            return False
    
    def verify_master_password(self, master_password: str) -> bool:
        """Verify if the master password is correct."""
        try:
            credentials = self.load_credentials(master_password)
            return bool(credentials)
        except:
            return False
